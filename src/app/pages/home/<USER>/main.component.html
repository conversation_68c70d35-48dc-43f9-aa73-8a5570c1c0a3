<div class="Full_Wrapper">
  <div class="section-margin">
    <div class="ImageButtonContainer">
      <ng-container *ngFor="let item of homeData?.tiles">
        <a *ngIf="!checkIsFullUrl(item.url)" [routerLink]="item.url">
          <div class="ImageContainer" [style]="{ color: item?.color }">
            <!-- Show image slider for Discover title -->
            <div *ngIf="isDiscoverTitle(item.title)" class="discover-slider-container">
              <img
                *ngFor="let image of discoverSliderImages; let i = index"
                [src]="image.primary_image"
                [class.active]="i === currentSlideIndex"
                alt="Discover Image"
                class="slider-image"
              />
              <div class="slider-overlay">
                <div class="featured-text">Featured Artwork</div>
                <div class="artwork-title">{{ discoverSliderImages[currentSlideIndex]?.artwork_title }}</div>
              </div>
            </div>
            <!-- Show regular image for other titles -->
            <img
              *ngIf="!isDiscoverTitle(item.title)"
              [src]="
                'https://www.terrain.art/cdn-cgi/image/width=1000,quality=52/' +
                item.image[0]?.url
              "
              alt="Featured"
            />
            <div class="ContainerGradient"></div>
            <div class="ImageTextContainer" [style]="{ color: item?.color }">
              <p class="ImageTextContainer_MainText">{{ item.title }}</p>
              <p class="ImageTextContainer_SubText">{{ item.sub_title }}</p>
            </div>
          </div>
        </a>
        <a *ngIf="checkIsFullUrl(item.url)" [href]="item.url">
          <div class="ImageContainer" [style]="{ color: item?.color }">
            <!-- Show image slider for Discover title -->
            <div *ngIf="isDiscoverTitle(item.title)" class="discover-slider-container">
              <img
                *ngFor="let image of discoverSliderImages; let i = index"
                [src]="image.primary_image"
                [class.active]="i === currentSlideIndex"
                alt="Discover Image"
                class="slider-image"
              />
              <div class="slider-overlay">
                <div class="featured-text">Featured Artwork</div>
                <div class="artwork-title">{{ discoverSliderImages[currentSlideIndex]?.artwork_title }}</div>
              </div>
            </div>
            <!-- Show regular image for other titles -->
            <img
              *ngIf="!isDiscoverTitle(item.title)"
              [src]="
                'https://www.terrain.art/cdn-cgi/image/width=1000,quality=52/' +
                item.image[0]?.url
              "
              alt="Featured"
            />
            <div class="ContainerGradient"></div>
            <div class="ImageTextContainer" [style]="{ color: item?.color }">
              <p class="ImageTextContainer_MainText">{{ item.title }}</p>
              <p class="ImageTextContainer_SubText">{{ item.sub_title }}</p>
            </div>
          </div>
        </a>
      </ng-container>
    </div>
  </div>
  <ng-container *ngFor="let item of homeData?.featured">
    <div *ngIf="item.publish" class="section-margin">
      <div class="Featured_Container">
        <div class="Featured_Text">
          <div class="FeaturedTextContainer">
            <p class="MainText">Featured</p>
            <a
              *ngIf="!checkIsFullUrl(item.url)"
              [routerLink]="item.url"
              style="color: var(--primary-font-color)"
            >
              <p class="SubText">{{ item.title }}</p></a
            >
            <a
              *ngIf="checkIsFullUrl(item.url)"
              [href]="item.url"
              target="_blank"
              style="color: var(--primary-font-color)"
            >
              <p class="SubText">{{ item.title }}</p></a
            >
            <a
              *ngIf="!checkIsFullUrl(item.sub_url)"
              [routerLink]="item.sub_url"
              style="color: var(--primary-font-color)"
            >
              <p class="ArtistName">{{ item.sub_title }}</p>
            </a>
            <a
              *ngIf="checkIsFullUrl(item.sub_url)"
              [href]="item.sub_url"
              target="_blank"
              style="color: var(--primary-font-color)"
            >
              <p class="ArtistName">{{ item.sub_title }}</p>
            </a>
            <a
              *ngIf="!checkIsFullUrl(item.url)"
              [routerLink]="item.url"
              class="BuyArt"
              >{{ item.button_text }}</a
            >
            <a
              *ngIf="checkIsFullUrl(item.url)"
              [href]="item.url"
              target="_blank"
              class="BuyArt"
              >{{ item.button_text }}</a
            >
          </div>
        </div>
        <div class="Featured_Image">
          <a
            *ngIf="!checkIsFullUrl(item.url)"
            [routerLink]="item.url"
            style="color: var(--primary-font-color)"
            ><img
              [src]="
                'https://www.terrain.art/cdn-cgi/image/width=1000,quality=52/' +
                item.image[0].url
              "
              alt="Featured Image"
          /></a>
          <a
            *ngIf="checkIsFullUrl(item.url)"
            [href]="item.url"
            target="_blank"
            style="color: var(--primary-font-color)"
            ><img
              [src]="
                'https://www.terrain.art/cdn-cgi/image/width=1000,quality=52/' +
                item.image[0].url
              "
              alt="Featured Image"
          /></a>
        </div>
        <a
          *ngIf="!checkIsFullUrl(item.url)"
          [routerLink]="item.url"
          class="BuyArtMobile"
          >{{ item.button_text }}</a
        >
        <a
          *ngIf="checkIsFullUrl(item.url)"
          [href]="item.url"
          target="_blank"
          class="BuyArtMobile"
          >{{ item.button_text }}</a
        >
      </div>
    </div>
  </ng-container>
</div>

<!-- Featured Artworks Popup -->
<div class="featured-popup" [class.show]="showFeaturedPopup">
  <div class="popup-header">
    <h3>Featured Artworks</h3>
    <button class="close-btn" (click)="closeFeaturedPopup()">×</button>
  </div>
  <div class="popup-content">
    <div class="popup-slider-container">
      <button class="slider-arrow left-arrow" (click)="previousPopupSlide()" [disabled]="popupSlideIndex === 0">
        &#8249;
      </button>
      <div class="popup-slider-wrapper">
        <div class="popup-slider-track" [style.transform]="'translateX(-' + (popupSlideIndex * 100) + '%)'">
          <div class="popup-slide" *ngFor="let artwork of discoverSliderImages; let i = index">
            <div class="artwork-item" (click)="navigateToDiscover()">
              <img [src]="artwork.primary_image" [alt]="artwork.artwork_title" />
              <div class="artwork-info">
                <h4>{{ artwork.artwork_title }}</h4>
                <p>{{ artwork.display_name }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <button class="slider-arrow right-arrow" (click)="nextPopupSlide()" [disabled]="popupSlideIndex === discoverSliderImages.length - 1">
        &#8250;
      </button>
    </div>
    <div class="popup-dots" *ngIf="discoverSliderImages.length > 1">
      <span
        class="dot"
        *ngFor="let artwork of discoverSliderImages; let i = index"
        [class.active]="i === popupSlideIndex"
        (click)="goToPopupSlide(i)">
      </span>
    </div>
  </div>
</div>

<div class="Background_Filler"></div>
