.Full_Wrapper {
  background-color: #f6f6f6;
  padding: 5.464vw 0;
}
.section-margin {
  padding: 0 6.997vw;
}
.ImageButtonContainer {
  display: flex;
  a {
    cursor: pointer;
    .ImageContainer {
      // background-image: url('../../../../assets/images/ar3.jpg') ;
      width: 42.38vw;
      height: 38.88vw;
      position: relative;
      img {
        width: 100%;
        height: 100%;
        border-radius: 1vw;
        -o-object-fit: cover;
        object-fit: cover;
      }
      .discover-slider-container {
        width: 100%;
        height: 100%;
        border-radius: 1vw;
        overflow: hidden;
        position: relative;

        .slider-image {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 1vw;
          opacity: 0;
          transition: opacity 0.5s ease-in-out;

          &.active {
            opacity: 1;
          }
        }
      }
      .ContainerGradient {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 1vw;
        overflow: hidden;
        background: var(--card-linear-gradient);
        @media (max-width: 768px) {
          border-radius: 2vw;
        }
      }
      .ImageTextContainer {
        position: absolute;
        width: 100%;
        top: 50%;
        transform: translate(0, -50%);
        //font-weight: 600;
        font-family: var(--primary-font);
        padding: 0 6.931vw;
        text-align: center;
        color: #ffffff;
        p {
          margin-bottom: unset;
        }
        .ImageTextContainer_MainText {
          font-size: 3.165vw;
          margin-bottom: 2.665vw;
        }
        .ImageTextContainer_SubText {
          margin-top: 2.665vw;
          font-size: 1.766vw;
        }
      }
    }
    .ImageContainer:first-child {
      margin-right: 1.232vw;
    }
  }
}
.Featured_Container {
  border-radius: 1vw;
  overflow: hidden;
  margin-top: 7.097vw;
  height: 27.6241vw;
  display: flex;
  //justify-content: center;
  flex-direction: row-reverse;
  background-color: white;
  .Featured_Text {
    background-color: white;
    width: 50%;
    //display: flex;
    align-self: center;
    padding-left: 4.165vw;
    //display: flex;
    //justify-content: center;
    //padding: 4.165vw 4.165vw 4.165vw 4.165vw;
    .FeaturedTextContainer {
      display: inline-block;
    }
    p {
      margin-bottom: unset;
    }
    .MainText {
      display: table;
      font-size: 2.332vw;
      color: white;
      padding: 0.533vw 0.832vw;
      background-color: black;
      margin-bottom: 3.232vw;
    }
    .SubText {
      margin-bottom: 2.065vw;
      //padding-left: 0.832vw;
      font-size: 2.499vw;
      font-style: italic;
      &.small {
        font-size: 2.1vw;
      }
    }
    .ArtistName {
      margin-bottom: 2.132vw;
      //padding-left: 0.832vw;
      font-size: 1.266vw;
    }
    .BuyArt {
      // margin-left: 0.832vw;
      background-color: black;
      color: white;
      border-radius: 3vw;
      padding: 0.799vw 1.666vw;
    }
  }
  .Featured_Image {
    width: 50%;

    img {
      width: 100%;
      height: 100%;
      -o-object-fit: cover;
      object-fit: cover;
    }
  }
  .BuyArtMobile {
    display: none;
  }
}
// .Background_Filler{
//   position: absolute;
//   z-index: -1;
//   top :34.388vw;
//   width: 100%;
//   height: 56.047vw;
//   background-color: #F6F6F6;
// }

@media (max-width: 768px) {
  .section-margin {
    padding: 0 5.909vw;
  }
  .ImageButtonContainer {
    display: flex;
    flex-direction: column;

    a {
      .ImageContainer {
        width: 88.18vw;
        height: 88.18vw;
        position: relative;
        img {
          width: 100%;
          height: 100%;
          border-radius: 2vw;
        }
        .discover-slider-container {
          width: 100%;
          height: 100%;
          border-radius: 2vw;
          overflow: hidden;
          position: relative;

          .slider-image {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 2vw;
            opacity: 0;
            transition: opacity 0.5s ease-in-out;

            &.active {
              opacity: 1;
            }
          }
        }
        .ImageTextContainer {
          position: absolute;
          width: 100%;
          top: 50%;
          transform: translate(0, -50%);
          //font-weight: 600;
          font-family: var(--primary-font);
          padding: 0 6.931vw;
          text-align: center;
          color: #ffffff;
          p {
            margin-bottom: unset;
          }
          .ImageTextContainer_MainText {
            font-size: 9.501vw;
            margin-bottom: 8.458vw;
          }
          .ImageTextContainer_SubText {
            margin-top: unset;
            font-size: 4.403vw;
          }
        }
      }
      .ImageContainer:first-child {
        // margin-right: 1.232vw;
        margin-bottom: 6vw;
      }
    }
  }
  .Featured_Container {
    margin-top: 7.097vw;
    // height: 129.89vw;
    height: 100%;
    flex-direction: column;
    background-color: white;
    border-radius: 2vw;
    overflow: hidden;
    .Featured_Text {
      background-color: white;
      width: 100%;
      padding: 4.165vw 4.165vw 4.165vw 4.165vw;
      text-align: center;
      p {
        margin-bottom: unset;
      }
      .MainText {
        display: inline-table;
        justify-content: center;
        font-size: 5.793vw;
        color: white;
        padding: 1.39vw 3.476vw;
        background-color: black;
        margin-bottom: 5.214vw;
      }
      .SubText {
        text-align: center;
        margin-bottom: 3.128vw;
        padding-left: 0.832vw;
        font-size: 5.33vw;
        font-style: italic;
        &.small {
          font-size: 5.33vw;
        }
      }
      .ArtistName {
        margin-bottom: unset;
        padding-left: 0.832vw;
        font-size: 3.244vw;
      }
      .BuyArt {
        display: none;
        margin-left: 0.832vw;
        background-color: black;
        color: white;
        border-radius: 3vw;
        padding: 0.799vw 1.666vw;
      }
    }
    .Featured_Image {
      width: 100%;

      img {
        width: 100%;
        height: 66.859vw;
      }
    }
    .BuyArtMobile {
      font-size: 3.823vw;
      cursor: pointer;
      margin: 5.098vw auto;
      display: block;
      background-color: black;
      color: white;
      border-radius: 10vw;
      padding: 3.476vw 5.793vw;
    }
  }
}
