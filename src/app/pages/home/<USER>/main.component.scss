.Full_Wrapper {
  background-color: #f6f6f6;
  padding: 5.464vw 0;
}
.section-margin {
  padding: 0 6.997vw;
}
.ImageButtonContainer {
  display: flex;
  a {
    cursor: pointer;
    .ImageContainer {
      // background-image: url('../../../../assets/images/ar3.jpg') ;
      width: 42.38vw;
      height: 38.88vw;
      position: relative;
      img {
        width: 100%;
        height: 100%;
        border-radius: 1vw;
        -o-object-fit: cover;
        object-fit: cover;
      }
      .discover-slider-container {
        width: 100%;
        height: 100%;
        border-radius: 1vw;
        overflow: hidden;
        position: relative;

        .slider-image {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 1vw;
          opacity: 0;
          transition: opacity 0.5s ease-in-out;

          &.active {
            opacity: 1;
          }
        }

        .slider-overlay {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          display: flex;
          justify-content: space-between;
          align-items: flex-end;
          padding: 2vw;
          background: linear-gradient(transparent, rgba(0, 0, 0, 0.3));
          z-index: 10;

          .featured-text {
            color: white;
            font-size: 1.2vw;
            font-family: var(--primary-font);
            letter-spacing: 0.1em;
          }

          .artwork-title {
            color: white;
            font-size: 1.4vw;
            font-weight: 500;
            font-family: var(--primary-font);
            text-align: right;
            font-style: italic;
          }
        }
      }
      .ContainerGradient {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 1vw;
        overflow: hidden;
        background: var(--card-linear-gradient);
        @media (max-width: 768px) {
          border-radius: 2vw;
        }
      }
      .ImageTextContainer {
        position: absolute;
        width: 100%;
        top: 50%;
        transform: translate(0, -50%);
        //font-weight: 600;
        font-family: var(--primary-font);
        padding: 0 6.931vw;
        text-align: center;
        color: #ffffff;
        p {
          margin-bottom: unset;
        }
        .ImageTextContainer_MainText {
          font-size: 3.165vw;
          margin-bottom: 2.665vw;
        }
        .ImageTextContainer_SubText {
          margin-top: 2.665vw;
          font-size: 1.766vw;
        }
      }
    }
    .ImageContainer:first-child {
      margin-right: 1.232vw;
    }
  }
}
.Featured_Container {
  border-radius: 1vw;
  overflow: hidden;
  margin-top: 7.097vw;
  height: 27.6241vw;
  display: flex;
  //justify-content: center;
  flex-direction: row-reverse;
  background-color: white;
  .Featured_Text {
    background-color: white;
    width: 50%;
    //display: flex;
    align-self: center;
    padding-left: 4.165vw;
    //display: flex;
    //justify-content: center;
    //padding: 4.165vw 4.165vw 4.165vw 4.165vw;
    .FeaturedTextContainer {
      display: inline-block;
    }
    p {
      margin-bottom: unset;
    }
    .MainText {
      display: table;
      font-size: 2.332vw;
      color: white;
      padding: 0.533vw 0.832vw;
      background-color: black;
      margin-bottom: 3.232vw;
    }
    .SubText {
      margin-bottom: 2.065vw;
      //padding-left: 0.832vw;
      font-size: 2.499vw;
      font-style: italic;
      &.small {
        font-size: 2.1vw;
      }
    }
    .ArtistName {
      margin-bottom: 2.132vw;
      //padding-left: 0.832vw;
      font-size: 1.266vw;
    }
    .BuyArt {
      // margin-left: 0.832vw;
      background-color: black;
      color: white;
      border-radius: 3vw;
      padding: 0.799vw 1.666vw;
    }
  }
  .Featured_Image {
    width: 50%;

    img {
      width: 100%;
      height: 100%;
      -o-object-fit: cover;
      object-fit: cover;
    }
  }
  .BuyArtMobile {
    display: none;
  }
}
// .Background_Filler{
//   position: absolute;
//   z-index: -1;
//   top :34.388vw;
//   width: 100%;
//   height: 56.047vw;
//   background-color: #F6F6F6;
// }

@media (max-width: 768px) {
  .section-margin {
    padding: 0 5.909vw;
  }
  .ImageButtonContainer {
    display: flex;
    flex-direction: column;

    a {
      .ImageContainer {
        width: 88.18vw;
        height: 88.18vw;
        position: relative;
        img {
          width: 100%;
          height: 100%;
          border-radius: 2vw;
        }
        .discover-slider-container {
          width: 100%;
          height: 100%;
          border-radius: 2vw;
          overflow: hidden;
          position: relative;

          .slider-image {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 2vw;
            opacity: 0;
            transition: opacity 0.5s ease-in-out;

            &.active {
              opacity: 1;
            }
          }

          .slider-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
            padding: 4vw;
            background: linear-gradient(transparent, rgba(0, 0, 0, 0.4));
            z-index: 10;

            .featured-text {
              color: white;
              font-size: 3.5vw;
              font-weight: 600;
              font-family: var(--primary-font);
              text-transform: uppercase;
              letter-spacing: 0.1em;
            }

            .artwork-title {
              color: white;
              font-size: 4vw;
              font-weight: 500;
              font-family: var(--primary-font);
              text-align: right;
              font-style: italic;
              max-width: 50%;
              line-height: 1.2;
            }
          }
        }
        .ImageTextContainer {
          position: absolute;
          width: 100%;
          top: 50%;
          transform: translate(0, -50%);
          //font-weight: 600;
          font-family: var(--primary-font);
          padding: 0 6.931vw;
          text-align: center;
          color: #ffffff;
          p {
            margin-bottom: unset;
          }
          .ImageTextContainer_MainText {
            font-size: 9.501vw;
            margin-bottom: 8.458vw;
          }
          .ImageTextContainer_SubText {
            margin-top: unset;
            font-size: 4.403vw;
          }
        }
      }
      .ImageContainer:first-child {
        // margin-right: 1.232vw;
        margin-bottom: 6vw;
      }
    }
  }
  .Featured_Container {
    margin-top: 7.097vw;
    // height: 129.89vw;
    height: 100%;
    flex-direction: column;
    background-color: white;
    border-radius: 2vw;
    overflow: hidden;
    .Featured_Text {
      background-color: white;
      width: 100%;
      padding: 4.165vw 4.165vw 4.165vw 4.165vw;
      text-align: center;
      p {
        margin-bottom: unset;
      }
      .MainText {
        display: inline-table;
        justify-content: center;
        font-size: 5.793vw;
        color: white;
        padding: 1.39vw 3.476vw;
        background-color: black;
        margin-bottom: 5.214vw;
      }
      .SubText {
        text-align: center;
        margin-bottom: 3.128vw;
        padding-left: 0.832vw;
        font-size: 5.33vw;
        font-style: italic;
        &.small {
          font-size: 5.33vw;
        }
      }
      .ArtistName {
        margin-bottom: unset;
        padding-left: 0.832vw;
        font-size: 3.244vw;
      }
      .BuyArt {
        display: none;
        margin-left: 0.832vw;
        background-color: black;
        color: white;
        border-radius: 3vw;
        padding: 0.799vw 1.666vw;
      }
    }
    .Featured_Image {
      width: 100%;

      img {
        width: 100%;
        height: 66.859vw;
      }
    }
    .BuyArtMobile {
      font-size: 3.823vw;
      cursor: pointer;
      margin: 5.098vw auto;
      display: block;
      background-color: black;
      color: white;
      border-radius: 10vw;
      padding: 3.476vw 5.793vw;
    }
  }
}

// Featured Artworks Popup
.featured-popup {
  position: fixed;
  bottom: 2vw;
  right: 2vw;
  width: 25vw;
  max-height: 60vh;
  background: white;
  border-radius: 1vw;
  box-shadow: 0 0.5vw 2vw rgba(0, 0, 0, 0.2);
  z-index: 1000;
  opacity: 0;
  transform: translateY(2vw);
  transition: all 0.3s ease-in-out;
  overflow: hidden;

  &.show {
    opacity: 1;
    transform: translateY(0);
  }

  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5vw 2vw;
    border-bottom: 1px solid #eee;
    background: #f8f8f8;

    h3 {
      margin: 0;
      font-size: 1.4vw;
      font-weight: 600;
      color: #333;
      font-family: var(--primary-font);
    }

    .close-btn {
      background: none;
      border: none;
      font-size: 2vw;
      color: #666;
      cursor: pointer;
      padding: 0;
      width: 2vw;
      height: 2vw;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      transition: all 0.2s ease;

      &:hover {
        background: #e0e0e0;
        color: #333;
      }
    }
  }

  .popup-content {
    max-height: 45vh;
    overflow-y: auto;
    padding: 1vw;

    .artwork-item {
      display: flex;
      align-items: center;
      padding: 1vw;
      border-radius: 0.5vw;
      margin-bottom: 0.5vw;
      transition: background 0.2s ease;
      cursor: pointer;

      &:hover {
        background: #f5f5f5;
      }

      &:last-child {
        margin-bottom: 0;
      }

      img {
        width: 4vw;
        height: 4vw;
        object-fit: cover;
        border-radius: 0.3vw;
        margin-right: 1vw;
      }

      .artwork-info {
        flex: 1;

        h4 {
          margin: 0 0 0.3vw 0;
          font-size: 1.1vw;
          font-weight: 600;
          color: #333;
          font-family: var(--primary-font);
        }

        p {
          margin: 0;
          font-size: 0.9vw;
          color: #666;
          font-family: var(--primary-font);
        }
      }
    }
  }

  // Mobile styles for popup
  @media (max-width: 768px) {
    .featured-popup {
      bottom: 4vw;
      right: 4vw;
      width: 80vw;
      max-height: 50vh;

      .popup-header {
        padding: 3vw 4vw;

        h3 {
          font-size: 4.5vw;
        }

        .close-btn {
          font-size: 6vw;
          width: 6vw;
          height: 6vw;
        }
      }

      .popup-content {
        padding: 2vw;

        .artwork-item {
          padding: 2vw;

          img {
            width: 12vw;
            height: 12vw;
            margin-right: 3vw;
          }

          .artwork-info {
            h4 {
              font-size: 3.5vw;
              margin-bottom: 1vw;
            }

            p {
              font-size: 2.8vw;
            }
          }
        }
      }
    }
  }
}
