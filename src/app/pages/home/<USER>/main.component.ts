import { ExploreService } from './../../explore/services/explore.service';
import { LearnPageService } from './../../learn/services/learn.service';
import { HomeService } from './../services/home.service';
import { bannerDemo2 } from './../../../demo/banner-slider.demo';
import { galleriesDemo } from './../../../demo/galleries.demo';
import { faCube, faImage } from '@fortawesome/free-solid-svg-icons';

import {
  Component,
  ElementRef,
  HostListener,
  Inject,
  OnDestroy,
  OnInit,
  Renderer2,
  ViewChild,
} from '@angular/core';
import {
  faPlay,
  faAngleDown,
  faChevronRight,
} from '@fortawesome/free-solid-svg-icons';
import { artworksDemo } from 'src/app/demo/artworks.demo';
import { exhibitionDemo } from 'src/app/demo/exhibitonslider.demo';
import { HomePageModel } from '../models/home-page.model';
import { BannerSliderItem } from 'src/app/core/banner-slider/banner-slider.model';
import { Artwork } from '../../explore/artists/models/artwork.model';
import {
  MediaForSlider,
  SlideMediaType,
} from 'src/app/core/single-slider/single-slider.model';

import { Meta, Title } from '@angular/platform-browser';
import { ExhibitionService } from '../../explore/services/exhibition.service';
import { ArtResourceService } from '../../explore/services/art-resource.service';
import { ArtResourceModel } from '../../explore/models/art-resource.model';
import { CardBoxItem } from 'src/app/core/card-box/card-box.model';
import { JsonLDService } from 'src/app/shared/services/json-ld.service';
import { Subscription } from 'rxjs';
import { ActivatedRoute, NavigationEnd, NavigationStart, Router } from '@angular/router';
import { DOCUMENT } from '@angular/common';
import { LearnPageModel } from '../../learn/models/learn-page.model';
import { ExplorePageModel } from '../../explore/models/explore-page.model';
import {
  NavBarService,
  NavBarType,
} from './../../../shared/services/navbar.service';
import { apiUrl } from 'src/environments/environment.prod';
import { CollectorService } from 'src/app/services/collector.service';
@Component({
  selector: 'app-main',
  templateUrl: './main.component.html',
  styleUrls: ['./main.component.scss'],
})
export class MainComponent implements OnInit, OnDestroy {
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.isMobile = this.getIsMobile();
  }
  learnData: LearnPageModel;
  artistData: CardBoxItem[];
  blogList = [];
  faAngleDown = faAngleDown;
  faChevronRight = faChevronRight;
  artworks: Artwork[] = [];
  limitedEdition: CardBoxItem[];
  galleries = galleriesDemo;
  exhibionSlider: MediaForSlider[] = [];
  bannerData: BannerSliderItem[] = [];
  exhibionSlider2: MediaForSlider[] = [];
  faCube = faCube;
  faImage = faImage;
  exhibitonsData = [];
  exploreData: ExplorePageModel;

  start = 0;
  end = 0;

  homeData: any;

  // Discover slider images
  discoverSliderImages = [
    {
      url: '/discover',
      primary_image: 'https://www.terrain.art/cdn-cgi/image/width=1000,quality=52/https://ta-python.s3.us-east-2.amazonaws.com/1747469046004_maze%20.jpg',
      artwork_title: 'Maze',
      display_name: 'Contemporary Art'
    },
    {
      url: '/discover',
      primary_image: 'https://www.terrain.art/cdn-cgi/image/width=1000,quality=52/https://ta-python.s3.us-east-2.amazonaws.com/1748947575390_IMG_1341.jpeg',
      artwork_title: 'Untitled',
      display_name: 'Modern Expression'
    },
    {
      url: '/discover',
      primary_image: 'https://www.terrain.art/cdn-cgi/image/width=1000,quality=52/https://ta-python.s3.us-east-2.amazonaws.com/1748412729400_Waltzing%20The%20Wild%204.jpg',
      artwork_title: 'Waltzing The Wild 4',
      display_name: 'Abstract Collection'
    }
  ];

  isMobile = false;
  roterObserver: Subscription;

  // Slider properties
  currentSlideIndex = 0;
  sliderInterval: any;

  websiteSchema = {
    '@context': 'http://schema.org',
    '@type': 'Organization',
    address: {
      '@type': 'PostalAddress',
      addressLocality: 'New Delhi, India',
      postalCode: '110019',
      streetAddress: '703, Chiranjiv Tower,43 Nehru Place',
    },
    email: '<EMAIL>',
    name: 'Terrain.Art',
    founder: {
      '@type': 'Person',
      name: 'Aparajita Jain',
    },
    foundingDate: '2019',
    location: 'New Delhi, India',
    telephone: '+91 011 40687117',
    parentOrganization: {
      '@type': 'Organization',
      name: 'Repra Art Private Limited',
    },
  };

  private jsonLdScriptElement: HTMLScriptElement;
  private JSON_LD_SELECTOR = 'script[type="application/ld+json"]';

  constructor(
    private homeService: HomeService,
    private navBarService: NavBarService,
    private titleService: Title,
    private metaService: Meta,
    private exploreService: ExploreService,
    private learnPageService: LearnPageService,
    private exhibitionService: ExhibitionService,
    private jsonLDService: JsonLDService,
    public router: Router,
    private server: CollectorService,
    @Inject(DOCUMENT) private _document: Document,
    private renderer: Renderer2,
    private el: ElementRef,
    private Activatedroute: ActivatedRoute
  ) {
    //this.jsonLDService.insertSchema(this.websiteSchema);
    // this.roterObserver = this.router.events.subscribe((event) => {
    //   if (event instanceof NavigationEnd) {
    //     this.jsonLDService.insertSchema(this.websiteSchema);
    //   }
    //   if (event instanceof NavigationStart) {
    //     this.jsonLDService.removeStructuredData();
    //   }
    // });
  }
  ngOnDestroy(): void {
    this.navBarService.changeNavbarType(NavBarType.DEFAULT);
    //this.roterObserver.unsubscribe();
    this.metaService.removeTag("name='google-site-verification'");
    if (this.jsonLdScriptElement) {
      this.renderer.removeChild(
        this.el.nativeElement.ownerDocument.head,
        this.jsonLdScriptElement
      );
    }
    // Clear slider interval
    if (this.sliderInterval) {
      clearInterval(this.sliderInterval);
    }
  }

  ngOnInit(): void {
    this.verifyEmail();
    this.navBarService.changeNavbarType(NavBarType.TRANSPARENT);
    this.setSEOTags();
    this.getPageData();
    this.isMobile = this.getIsMobile();
    this.startSlider();

    window.onresize = () => {
      this.isMobile = this.getIsMobile();
    };
  }
  public getPageData() {
    let url = `website?page=home`;
    this.server.getApi(url).subscribe((res) => {
      if (res.statusCode == 200 && res.data) {
        this.homeData = res.data.contents;
      }
    });
  }

  public getIsMobile(): boolean {
    const w = document.documentElement.clientWidth;
    const breakpoint = 768;
    if (w <= breakpoint) {
      this.isMobile = true;
      return true;
    } else {
      this.isMobile = false;
      return false;
    }
  }

  // public get getSliderData(): any[] {
  //   let data: {
  //     routerLink: string;
  //     image: string;
  //     heading: string;
  //     subheading: string;
  //   }[] = [];
  //   for (let index = 0; index < 3; index++) {
  //     const element = {
  //       routerLink:
  //         '/explore/artists/' +
  //         this.homeData?.featured_artist_select[index]['01_personal_info_id']
  //           ?.url_name,
  //       image:
  //         'https://www.terrain.art/cdn-cgi/image/width=1000,quality=52/https://register.terrain.art/artist-portal/assets/' +
  //         this.homeData?.featured_artist_select[index]['01_personal_info_id']
  //           ?.artist_list_thumbnail?.private_hash,
  //       heading:
  //         this.homeData?.featured_artist_select[index]['01_personal_info_id']
  //           ?.display_name,
  //       subheading: '',
  //     };
  //     data.push(element);
  //   }
  //   return data;
  // }
  public get artworksList(): Artwork[] {
    const list: Artwork[] = this.artworks;
    return list;
  }

  setSEOTags() {
    this.titleService.setTitle('Terrain.Art');
    this.metaService.updateTag({
      name: 'keywords',
      content: 'art collection, indian artists, art history, art terminology',
    });
    this.metaService.updateTag({
      name: 'description',
      content:
        'Build your art collection from an exciting range of Indian artists, Learn South Asian art history and art terminology, Explore archives of South Asian artists.',
    });
    this.metaService.updateTag({
      name: 'og:image',
      content: 'https://www.terrain.art/assets/fav.png',
    });
    this.metaService.updateTag({ name: 'og:title', content: 'Terrain.Art' });
    this.metaService.updateTag({ name: 'og:title', content: 'Terrain.Art' });
    this.metaService.updateTag({
      name: 'og:description',
      content:
        'Build your art collection from an exciting range of Indian artists, Learn South Asian art history and art terminology, Explore archives of South Asian artists.',
    });
    this.metaService.addTags([
      {
        name: 'google-site-verification',
        content: 'IpPkhQOvg-k4defKxat-z6zsEyrD8VF6d15cvmCJMuc',
      },
    ]);
    const structuredData = [
      {
        '@context': 'http://schema.org',
        '@type': 'Organization',
        address: {
          '@type': 'PostalAddress',
          addressLocality: 'New Delhi, India',
          postalCode: '110019',
          streetAddress: '703, Chiranjiv Tower,43 Nehru Place',
        },
        url: 'https://www.terrain.art/',
        logo: 'https://www.terrain.art/assets/images/logo.png',
        email: '<EMAIL>',
        name: 'Terrain.Art',
        founder: {
          '@type': 'Person',
          name: 'Aparajita Jain',
        },
        foundingDate: '2019',
        location: 'New Delhi, India',
        telephone: '+91 011 40687117',
        parentOrganization: {
          '@type': 'Organization',
          name: 'Repra Art Private Limited',
        },
        sameAs: [
          'https://twitter.com/terrain_art',
          'https://www.facebook.com/the.terrain.art',
          'https://www.instagram.com/terrain_art/',
        ],
      },
      {
        '@context': 'https://schema.org',
        '@type': 'WebSite',
        url: 'https://www.terrain.art/',
        potentialAction: {
          '@type': 'SearchAction',
          target: {
            '@type': 'EntryPoint',
            urlTemplate:
              'https://www.terrain.art/marketplace?search={search_term_string}',
          },
          'query-input': 'required name=search_term_string',
        },
      },
    ];
    this.jsonLdScriptElement =
      this.el.nativeElement.ownerDocument.querySelector(this.JSON_LD_SELECTOR);

    if (this.jsonLdScriptElement) {
      this.renderer.setProperty(
        this.jsonLdScriptElement,
        'innerHTML',
        JSON.stringify(structuredData)
      );
    } else {
      this.jsonLdScriptElement = this.renderer.createElement('script');
      this.renderer.setAttribute(
        this.jsonLdScriptElement,
        'type',
        'application/ld+json'
      );
      this.renderer.setProperty(
        this.jsonLdScriptElement,
        'innerHTML',
        JSON.stringify(structuredData)
      );
      this.renderer.appendChild(
        this.el.nativeElement.ownerDocument.head,
        this.jsonLdScriptElement
      );
    }
  }
  verifyEmail() {
    this.Activatedroute.queryParamMap.subscribe(async (params) => {
      let option = 0;
      if (params.get('token')) {
        let url = apiUrl.emailVerification + `/${params.get('token')}`;
        this.server.getApi(url).subscribe((res) => {
          console.log(res);
          if (res.statusCode == 200) {
            alert(
              'Email verified successfully.Please setup password.Please login!'
            );
            localStorage.setItem('userId', res.data.userId);
            // this.router.navigate(['setup-pass']);
          } else {
            alert(res.message || res.Message);
          }
        });
      }
    });

  }
  checkIsFullUrl(url) {
    return url.match(/^(http|https):\/\/[^\s\/]+[\s\/]+/);
  }

  isDiscoverTitle(title: string): boolean {
    return title && title.toLowerCase() === 'discover';
  }

  startSlider(): void {
    // Only start slider if we have images
    if (this.discoverSliderImages.length > 1) {
      this.sliderInterval = setInterval(() => {
        this.currentSlideIndex = (this.currentSlideIndex + 1) % this.discoverSliderImages.length;
      }, 3000); // Change image every 2 seconds
    }
  }
}
